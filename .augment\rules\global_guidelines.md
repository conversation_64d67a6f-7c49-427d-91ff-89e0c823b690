---
type: "always_apply"
---

# Agent 通用开发指南 (global_guidelines.md)

本文档为在 IDE 中使用的 Augment AI Agent 提供一套通用的规则和指南。请严格遵守这些指南以确保项目代码的统一、规范和高质量。

---

## Agent 交互准则

**【禁止废话】**: 你的回答**只应包含代码**。除非我特别要求，否则不要添加任何解释、注释或总结。

---

## 核心规则 (Always - 始终遵循)

这些是最高优先级的规则，你在每一次交互中都必须严格遵守。

1.  **【遵循约定】**: 你的首要任务是理解并严格遵守项目中已有的结构、命名约定和编码风格。绝不引入任何破坏现有约定的变更。
2.  **【单一实现路径】**: 对于任何功能，只提供一种最直接、最简单的实现路径。禁止提供备用方案、多种尝试方法或复杂的选择逻辑。
3.  **【最少代码】**: 只编写为实现我要求所必需的最少代码。当用户要求"添加功能"时，只实现一个最基本的测试用例，除非用户明确要求更多。
4.  **【立即失败原则】**: 当检测到错误或异常情况时，立即停止执行并明确报告问题。绝不隐藏错误或尝试绕过问题。
5.  **【禁止复杂异常处理】**: 绝不使用复杂的 `try...except` 结构来掩盖问题。让代码在出错时直接失败，以便快速定位和解决问题。
6.  **【最小可行实现】**: 当用户要求添加功能时，只实现一个最基本的测试用例。绝不主动添加多个测试场景、边界情况或错误处理测试，除非用户明确要求。
7.  **【先分析，后行动】**: 在创建或修改任何文件之前，你必须先读取相关的现有文件来理解上下文。当用户要求"先探索，再写代码"时，必须严格按此顺序执行。

---

## 通用异常处理原则

**正确的方式**:
- 直接检查条件并在失败时立即停止执行
- 使用明确的错误描述说明问题所在
- 让错误快速暴露，便于定位和解决

**错误的方式**:
- 使用复杂的异常捕获机制掩盖问题
- 尝试多种备用方案或恢复策略
- 隐藏错误信息或继续执行有问题的流程