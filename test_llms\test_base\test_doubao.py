import os
import pytest
import tempfile


@pytest.fixture
def doubao_agent(chat_helper_factory, cfg):
    doubao_agent = chat_helper_factory(
        platform_name="doubao",
        user_email=cfg.doubao.user_email,
        conversation_params={
            "enable_deep_thinking": cfg.doubao.enable_deep_thinking
        }
    )
    return doubao_agent

def test_basic_chat(cfg, ctx, doubao_agent):
    """测试基础聊天功能"""
    response = doubao_agent.chat(cfg.doubao.basic_chat_prompt)
    
    assert response is not None, "响应为空"
    assert len(response.strip()) > 0, "响应内容为空"
    assert cfg.doubao.expected_keyword in response, f"响应中未包含期望关键词: {cfg.doubao.expected_keyword}"
    
    ctx.basic_chat_response = response


def test_file_upload_txt(cfg, ctx, doubao_agent):
    """测试TXT文件上传功能"""
    # 创建临时TXT文件
    with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt', encoding='utf-8') as temp_file:
        temp_file.write(cfg.doubao.test_file_content)
        temp_file_path = temp_file.name

    # 测试文件上传和AI阅读
    response = doubao_agent.chat(cfg.doubao.file_upload_prompt, attachments=[temp_file_path])
    
    assert response is not None, "文件上传响应为空"
    assert len(response.strip()) > 0, "文件上传响应内容为空"
    
    # 清理临时文件
    os.remove(temp_file_path)
    
    ctx.file_upload_response = response


def test_deep_thinking_feature(cfg, ctx, chat_helper_factory):
    """测试深度思考功能"""
    # 测试启用深度思考的配置
    doubao_agent = chat_helper_factory(
        platform_name="doubao",
        user_email=cfg.doubao.user_email,
        conversation_params={
            "enable_deep_thinking": True
        }
    )

    # 测试基础聊天功能
    response = doubao_agent.chat(cfg.doubao.deep_thinking_prompt)
    
    assert response is not None, "深度思考响应为空"
    assert len(response.strip()) > 0, "深度思考响应内容为空"
    
    ctx.deep_thinking_response = response


def test_new_conversation(cfg, ctx, doubao_agent):
    """测试新建会话功能"""
    # 创建新会话
    doubao_agent.driver.new_conversation({
        "enable_deep_thinking": False
    })
    
    # 在新会话中测试聊天
    response = doubao_agent.chat(cfg.doubao.basic_chat_prompt)
    
    assert response is not None, "新会话响应为空"
    assert len(response.strip()) > 0, "新会话响应内容为空"
    
    ctx.new_conversation_response = response


def test_is_ready(cfg, ctx, doubao_agent):
    """测试平台就绪状态检查"""
    is_ready = doubao_agent.driver.is_ready()
    
    assert isinstance(is_ready, bool), "is_ready 应该返回布尔值"
    
    ctx.is_ready_status = is_ready


def test_config_combinations(cfg, ctx, chat_helper_factory):
    """测试不同配置组合"""
    test_configs = cfg.doubao.test_configs
    results = {}

    for config_name, config in test_configs.items():
        # 创建带有特定配置的客户端
        doubao_agent = chat_helper_factory(
            platform_name="doubao",
            user_email=cfg.doubao.user_email,
            conversation_params=config
        )

        # 测试聊天功能
        response = doubao_agent.chat(cfg.doubao.basic_chat_prompt)
        
        assert response is not None, f"配置 {config_name} 测试响应为空"
        assert len(response.strip()) > 0, f"配置 {config_name} 测试响应内容为空"
        
        results[config_name] = response

    ctx.config_test_results = results


def test_error_handling(cfg, ctx, doubao_agent):
    """测试错误处理"""
    # 测试空提示
    try:
        response = doubao_agent.chat("")
        # 如果没有抛出异常，检查响应
        if response is not None:
            ctx.empty_prompt_response = response
    except Exception as e:
        ctx.empty_prompt_error = str(e)

    # 测试非常长的提示
    long_prompt = "测试" * 1000
    try:
        response = doubao_agent.chat(long_prompt)
        if response is not None:
            ctx.long_prompt_response = response
    except Exception as e:
        ctx.long_prompt_error = str(e)


def test_feature_verification(cfg, ctx, chat_helper_factory):
    """测试功能验证"""
    # 创建带有所有功能的客户端
    doubao_agent = chat_helper_factory(
        platform_name="doubao",
        user_email=cfg.doubao.user_email,
        conversation_params={
            "enable_deep_thinking": True
        }
    )

    # 测试基础聊天功能
    response = doubao_agent.chat(cfg.doubao.basic_chat_prompt)
    
    assert response is not None, "功能验证测试响应为空"
    assert len(response.strip()) > 0, "功能验证测试响应内容为空"

    ctx.verification_results = {
        "basic_chat": response is not None and len(response.strip()) > 0,
        "deep_thinking": True,  # 假设深度思考功能已启用
        "response": response
    }


def test_verification_report(cfg, ctx):
    """生成功能验证报告"""
    if not hasattr(ctx, 'verification_results'):
        assert False, "需要先运行 test_feature_verification"

    results = ctx.verification_results

    # 生成报告
    report = []
    report.append("=== 豆包功能验证报告 ===")
    report.append(f"基础聊天功能: {'✓ 正常' if results['basic_chat'] else '✗ 异常'}")
    report.append(f"深度思考功能: {'✓ 启用' if results['deep_thinking'] else '✗ 未启用'}")
    report.append(f"响应内容: {'✓ 有效' if results['response'] else '✗ 无效'}")
    report.append("=" * 30)

    # 输出报告
    for line in report:
        print(line)

    # 至少基础聊天功能要正常
    assert results['basic_chat'], "基础聊天功能异常"
