import re
from playwright.sync_api import Page
from typing import Optional, List

from allm_helper.platforms.base_platform import BasePlatformDriver
from autils.pw_clipboard_helper import UniversalClipboardInterceptor


class DoubaoDriver(BasePlatformDriver):
    """
    豆包平台的具体驱动实现。
    """
    CHAT_URL = "https://www.doubao.com/chat/"
    INCOGNITO_URL = "https://www.doubao.com/chat/incognito"

    def __init__(self, page: Page):
        """
        初始化豆包驱动，只接受 page 参数。
        模型配置将在 new_conversation 时传入。
        """
        super().__init__(page)
        self.page.goto(self.CHAT_URL)

        # 默认配置值
        self.current_model_config = {
            'enable_deep_thinking': False,
            'use_incognito_mode': False,
        }

        # 文件上传相关设置
        self.upload_timeout = 10000  # 文件上传超时时间（毫秒）

    def new_conversation(self, model_config: Optional[dict] = None):
        """
        创建新会话，并应用模型配置。

        Args:
            model_config: 模型配置字典，包含：
                - enable_deep_thinking: 是否启用深度思考
                - use_incognito_mode: 是否使用临时会话模式
        """
        if model_config:
            self.current_model_config.update(model_config)

        # 等待页面完全加载
        self.page.wait_for_timeout(2000)

        # 根据配置决定是否使用临时会话模式
        if self.current_model_config.get('use_incognito_mode', False):
            self.page.goto(self.INCOGNITO_URL)
            self.page.wait_for_timeout(2000)
        else:
            # 点击新对话按钮
            new_chat_button = self.page.locator('button').filter(has_text="新对话").first
            if new_chat_button.is_visible():
                new_chat_button.click()
                self.page.wait_for_timeout(1000)

        # 应用模型配置
        self._apply_model_config()
        return self

    def use_existing_conversation(self, conversation_title: str):
        """使用已有会话"""
        # 在侧边栏查找指定标题的会话
        conversation_link = self.page.locator(f'text="{conversation_title}"').first
        assert conversation_link.is_visible(), f"未找到标题为 '{conversation_title}' 的会话"

        conversation_link.click()
        self.page.wait_for_timeout(2000)

    def start_incognito_conversation(self, model_config: Optional[dict] = None):
        """
        启动临时会话（无痕模式）

        Args:
            model_config: 模型配置字典
        """
        if model_config:
            self.current_model_config.update(model_config)

        # 强制设置为临时会话模式
        self.current_model_config['use_incognito_mode'] = True

        # 导航到临时会话页面
        self.page.goto(self.INCOGNITO_URL)
        self.page.wait_for_timeout(3000)

        # 应用模型配置
        self._apply_model_config()
        return self

    def _apply_model_config(self):
        """应用模型配置"""
        # 设置深度思考
        if 'enable_deep_thinking' in self.current_model_config:
            enable_thinking = self.current_model_config['enable_deep_thinking']
            self._toggle_deep_thinking(enable_thinking)

    def _toggle_deep_thinking(self, enable: bool):
        """切换深度思考功能"""
        # 查找深度思考按钮
        deep_thinking_button = self.page.locator('button').filter(has_text=re.compile(r'深度思考')).first
        if not deep_thinking_button.is_visible():
            return  # 如果找不到深度思考按钮，直接返回

        # 获取当前状态
        button_text = deep_thinking_button.text_content()
        current_enabled = "开启" in button_text or "自动" in button_text

        # 如果当前状态与期望状态不同，则点击切换
        if current_enabled != enable:
            deep_thinking_button.click()
            self.page.wait_for_timeout(1000)

    def _upload_files(self, files: List[str]) -> bool:
        """
        上传文件到豆包

        Args:
            files: 要上传的文件路径列表

        Returns:
            bool: 上传是否成功
        """
        # 首先尝试查找隐藏的文件输入元素
        file_input = self.page.locator('input[type="file"]').first
        if file_input.count() > 0:
            file_input.set_input_files(files)
            self.page.wait_for_timeout(2000)
            return True

        # 如果没有找到文件输入，尝试通过技能按钮访问文件上传
        skill_button = self.page.locator('button').filter(has_text="技能").first
        if skill_button.is_visible():
            skill_button.click()
            self.page.wait_for_timeout(1000)

            # 查找文件相关的技能选项
            file_option = self.page.locator('text*="文件"').first
            if file_option.is_visible():
                file_option.click()
                self.page.wait_for_timeout(1000)

                # 再次尝试查找文件输入
                file_input = self.page.locator('input[type="file"]').first
                if file_input.count() > 0:
                    file_input.set_input_files(files)
                    self.page.wait_for_timeout(2000)
                    return True

        return False

    def _wait_for_response_complete(self):
        """等待响应完成"""
        # 先等待3秒让消息发送
        self.page.wait_for_timeout(3000)

        # 等待AI回复出现（通过检测回复操作按钮的出现）
        copy_button_selector = '[data-testid="message_action_copy"]'

        # 尝试等待复制按钮出现，如果超时则继续
        if self.page.locator(copy_button_selector).count() > 0:
            # 如果已经有复制按钮，等待新的回复完成
            self.page.wait_for_timeout(2000)
        else:
            # 等待复制按钮出现，表示AI已经回复
            self.page.wait_for_selector(copy_button_selector, timeout=60000)

    def _get_reply_by_copy(self):
        """通过复制功能获取回复"""
        # 使用豆包特定的复制按钮
        copy_button = self.page.locator('[data-testid="message_action_copy"]').last
        if copy_button.is_visible():
            with UniversalClipboardInterceptor(self.page) as interceptor:
                copy_button.click()
                interceptor.wait_for_capture(timeout=5.0)
                if interceptor.text:
                    return interceptor.text

        # 备用方案1：通过消息容器获取最后的AI回复
        messages = self.page.locator('[class*="message"]').all()
        if messages:
            # 从后往前查找AI的回复（通常是偶数索引）
            for i in range(len(messages) - 1, -1, -1):
                message_text = messages[i].text_content()
                # 跳过用户的消息和空消息
                if message_text and not message_text.startswith("你好，请问") and len(message_text.strip()) > 10:
                    return message_text.strip()

        # 备用方案2：获取页面主要内容区域的文本
        main_content = self.page.locator('main').first
        if main_content.is_visible():
            content_text = main_content.text_content()
            # 简单解析获取最后的回复内容
            lines = content_text.split('\n')
            for line in reversed(lines):
                line = line.strip()
                if line and len(line) > 20 and not line.startswith("你好，请问"):
                    return line

        return "无法获取回复内容"

    def chat(self, prompt: str, attachments: Optional[List[str]] = None,
             response_format: str = "text", **kwargs) -> str:
        """
        在当前会话中发送消息并获取回复。

        :param prompt: 用户输入的提示。
        :param attachments: 要上传的附件的本地文件路径列表。
        :param response_format: 响应格式，可选 "text"。
        :return: AI的回复文本。
        """
        # 首先切换到当前驱动的页面
        self.switch_to_page()

        # 1. 上传附件 (如果需要)
        if attachments:
            self._upload_files(attachments)

        # 2. 输入提示
        # 使用豆包特定的输入框选择器
        input_element = self.page.locator('[data-testid="chat_input_input"]').first
        if not input_element.is_visible():
            # 备用选择器
            input_element = self.page.locator('textbox').filter(has_text=re.compile(r'发消息|输入')).first
            if not input_element.is_visible():
                input_element = self.page.locator('textarea, input[type="text"]').last

        assert input_element.is_visible(), "找不到可用的输入框"

        # 清空输入框并输入新文本
        input_element.click()
        input_element.fill("")  # 先清空
        input_element.fill(prompt)

        # 等待一下让发送按钮变为可用状态
        self.page.wait_for_timeout(500)

        # 3. 发送消息
        send_button = self.page.locator('button').filter(has_text="发送").first
        if send_button.is_visible() and not send_button.is_disabled():
            send_button.click()
        else:
            # 如果发送按钮不可用，尝试按回车键发送
            input_element.press("Enter")

        # 4. 等待回复完成并获取回复
        self._wait_for_response_complete()
        return self._get_reply_by_copy()

    def is_ready(self) -> bool:
        """检查平台是否准备好接收新消息"""
        # 检查用户头像按钮是否存在（表示已登录）
        avatar_button = self.page.locator('[data-testid="chat_header_avatar_button"]').first
        if not avatar_button.is_visible():
            return False

        # 检查输入框是否可用
        input_element = self.page.locator('[data-testid="chat_input_input"]').first
        if not input_element.is_visible():
            input_element = self.page.locator('textbox').filter(has_text=re.compile(r'发消息|输入')).first

        return input_element.is_visible()

    def save_chat(self, chat_name: str):
        """保存当前会话并命名"""
        # 豆包会自动保存会话，这里可以实现重命名功能
        pass

    def del_chat(self, chat_name: str):
        """删除会话"""
        # 在侧边栏找到指定会话并删除
        conversation_item = self.page.locator(f'text="{chat_name}"').first
        if not conversation_item.is_visible():
            return  # 如果找不到会话，直接返回

        # 右键点击或查找删除按钮
        conversation_item.click(button="right")
        self.page.wait_for_timeout(500)
        delete_button = self.page.locator('button').filter(has_text=re.compile(r'删除|delete')).first
        if delete_button.is_visible():
            delete_button.click()
            self.page.wait_for_timeout(1000)
