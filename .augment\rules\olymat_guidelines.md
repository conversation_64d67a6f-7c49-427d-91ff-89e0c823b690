---
type: "always_apply"
---

# Olymat 自动化框架开发指南 (olymat_guidelines.md)

本文档为在 IDE 中使用的 Augment AI Agent 提供一套 olymat 自动化框架的规则和指南。

---

## 核心规则 (Always - 始终遵循)

这些是 olymat 框架特定的最高优先级规则，你在每一次交互中都必须严格遵守。

1.  **【虚拟环境执行】**: **所有**的 Python 和 `pytest` 命令**必须**在项目的 `venv` 虚拟环境中执行。这意味着你的命令必须明确指向 `venv` 内的可执行文件。
    -   **正确示例**: `venv/Scripts/pytest.exe ...` 或 `venv/Scripts/python.exe ...`
    -   **错误示例**: `pytest ...` 或 `python ...`
2.  **【正确使用断言】**: 在测试脚本 (`*_test.py`) 中，使用正确的断言逻辑。当检测到异常情况时，使用 `assert condition, "具体原因"` 而不是 `assert False`。确保断言条件表达的是期望的正确状态。
3.  **【禁止测试日志】**: 在测试脚本中绝不使用任何形式的 `logging` 模块或 `print()` 语句进行调试。依赖测试框架的断言和输出来验证行为。
4.  **【禁止休眠等待】**: 绝不使用 `time.sleep()`。测试应该是确定性的，不应依赖于固定的等待时间。
5.  **【配置与代码分离】**: **严禁**在 Python 脚本中硬编码任何配置值。所有这些值都必须在 `.yaml` 配置文件中定义，并通过 `cfg` 对象在代码中访问。
6.  **【验证所有变更】**: 在完成任何代码修改后，你**必须**运行相关的 `pytest` 命令来验证你的变更没有引入错误，并且功能符合预期。
7.  **【正确使用 olymat 上下文】**: 在同一个测试流程中的不同测试函数之间传递数据时，**必须**使用 `ctx` 对象。注意 `ctx` 只能存储可序列化为 JSON 的基本数据类型（字符串、数字、布尔值、列表、字典），不能存储 Python 对象、函数或复杂类型。

---

## 场景触发规则 (Auto - 自动触发)

这些规则适用于特定的开发场景。当你识别出用户的意图与以下场景匹配时，应自动遵循相应的流程。

### 场景：当被要求“添加一个新功能”或“创建一个新测试”时 (开发阶段)

1.  **明确位置**: 首先向用户确认，这个新功能应该添加到哪个**工具集 (Toolset)** 的哪个**测试脚本**中。
2.  **探索阶段** (如果用户要求先探索): 使用 playwright/mcp 等工具先探索和理解功能，然后再编写代码。
3.  **定义运行时配置**: 打开 `[toolset]/confs/default.yaml`，为新功能添加并配置运行时参数。**此阶段禁止修改 `template.yaml`**。
4.  **最小实现**: 打开目标 `test_*.py` 文件，创建**一个**以 `test_` 开头的函数，该函数必须接受 `cfg` 和 `ctx` 作为参数。只实现最基本的功能测试。
5.  **运行验证**: 使用命令 `venv/Scripts/pytest.exe [脚本路径]::[新函数名]` 单独执行新函数进行验证。

### 场景：当被要求“修改一个现有功能”时

1.  **精准定位**: 找到并确认包含该功能的**工具集目录**和具体的 `test_*.py` 文件。
2.  **全面分析**: 阅读该函数的代码及其在 `confs/default.yaml` 中对应的配置。
3.  **执行修改**: 对 Python 代码或 `default.yaml` 中的配置进行修改。
4.  **运行验证**: 对被修改的函数运行 `pytest` 进行验证。

### 场景：当被要求“发布功能”、“配置UI”或“更新菜单”时 (发布阶段)

1.  **确认功能**: 向用户确认需要发布到 UI 菜单的具体是哪个**工具集 (Toolset)** 下的哪个**功能 (Function)**。
2.  **更新配置模板**: 从 `default.yaml` 文件中，将该功能对应的、已经验证过的配置块，复制并更新到 `[toolset]/confs/template.yaml` 文件中，作为该功能的标准模板。
3.  **更新UI菜单**: 打开 `[toolset]/olymat_ui/conf.yaml` 文件，在 `func_list` 列表中为要发布的功能添加一个新条目。
    - **示例**:
      ```yaml
      - name: "一个清晰的功能名称"
        cmd_param: "test_api_suite/test_user_auth.py"
      ```
4.  **告知完成**: 告知用户配置已更新。UI菜单的最终验证需要通过 olymat-ui 工具进行，这一步由用户完成。

---

## 手动引用规则 (Manual - 可手动@引用)

这部分是速查参考资料，你可以在需要时查阅，用户也可以通过 `@` 手动将其引入对话中。

### 参考：文件与命名约定

| 项目条目              | 命名约定                               | 示例                                  |
| --------------------- | -------------------------------------- | ------------------------------------- |
| **工具集目录**        | **必须**以 `test_` 开头                | `test_smoke_tests`                    |
| **测试脚本文件**      | **必须**以 `test_` 开头                | `test_user_auth.py`                   |
| **测试/功能函数**     | **必须**以 `test_` 开头                | `test_login_successful`               |
| **配置模板文件**      | **必须**命名为 `template.yaml`         | `test_smoke_tests/confs/template.yaml`|
| **UI配置文件**        | **必须**是 `olymat_ui/conf.yaml`       | `test_smoke_tests/olymat_ui/conf.yaml`|

### 参考：断言逻辑原则

**正确的断言逻辑**:
- 直接断言期望的正确状态，而不是检查错误状态后失败
- 使用明确的错误描述说明断言失败的原因
- 让断言表达业务逻辑的期望结果

**错误的断言逻辑**:
- 先检查错误条件，然后使用通用失败断言
- 使用反向逻辑表达期望状态
- 缺少明确的错误描述

### 参考：上下文数据传递原则

**正确的上下文使用**:
- 只存储可序列化为 JSON 的基本数据类型
- 存储字符串、数字、布尔值、列表、字典等简单数据
- 用于在测试函数之间传递状态信息和结果数据
- 存储测试过程中产生的关键标识符和计数值

**禁止的上下文使用**:
- 存储 Python 对象实例（如页面对象、客户端对象）
- 存储函数引用或方法对象
- 存储复杂的类实例或不可序列化的数据结构
- 尝试传递活跃的连接或会话对象

### 参考：测试实现原则

**最小可行测试原则**:
- 每次只实现一个最基本的测试函数
- 测试函数应该有明确的单一职责
- 从配置文件获取所有参数，避免硬编码
- 使用框架提供的上下文对象传递简单数据

**避免的测试反模式**:
- 一次实现多个测试用例（除非用户明确要求）
- 在单个测试中包含复杂的错误处理逻辑
- 使用硬编码的测试数据
- 使用调试输出语句或日志
- 依赖时间等待而非确定性检查

### 参考：命令执行原则

**测试执行策略**:
- 优先运行单个新增的测试函数进行验证
- 在虚拟环境中执行所有命令
- 使用框架提供的测试运行器
- 验证变更后运行相关测试确保无回归

**代码提交流程**:
- 检查当前代码状态
- 暂存所有相关变更
- 提供清晰的提交描述信息
